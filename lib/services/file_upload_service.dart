import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/utils/logger.dart';

class FileUploadService {
  final Dio _dio = Dio();
  final AuthService _authService = AuthService();

  // Base URL for the API
  final String _baseUrl = 'http://**********:8008/api/v1';

  // Method to pick a file
  Future<PlatformFile?> pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        return result.files.first;
      }

      return null;
    } catch (e) {
      Logger.error('Error picking file: $e');
      return null;
    }
  }

  // Method to upload a file
  Future<Map<String, dynamic>> uploadFile(PlatformFile file) async {
    try {
      // Get the token
      final token = await _authService.getValidToken();

      // Create form data
      FormData formData;

      if (kIsWeb) {
        // For web, use the bytes
        formData = FormData.fromMap({
          'file': MultipartFile.fromBytes(
            file.bytes!,
            filename: file.name,
          ),
        });
      } else {
        // For mobile, use the file path
        formData = FormData.fromMap({
          'file': await MultipartFile.fromFile(
            file.path!,
            filename: file.name,
          ),
        });
      }

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
      };

      // Make the API call
      final response = await _dio.post(
        '$_baseUrl/upload',
        data: formData,
        options: Options(headers: headers),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('File uploaded successfully: ${response.data}');

        // Parse the response using our model
        // The response now includes OCR data
        final fileUploadOcrResponse =
            FileUploadOcrResponse.fromJson(response.data);

        // Extract the OCR text
        final extractedText = fileUploadOcrResponse.data?.correctedText ??
            fileUploadOcrResponse.data?.originalText ??
            '';
        Logger.info('Extracted text from uploaded file: $extractedText');

        return {
          'success': true,
          'data': response.data,
          'file_name': file.name,
          'file_upload_ocr_response': fileUploadOcrResponse,
          'extracted_text': extractedText,
          'file_path': 'uploads/${file.name}',
          // fileUploadOcrResponse.output.filePath,
        };
      } else {
        Logger.error('Error uploading file: ${response.statusCode}');
        return {
          'success': false,
          'message': 'Error uploading file: ${response.statusCode}',
        };
      }
    } catch (e) {
      Logger.error('Exception uploading file: $e');
      return {
        'success': false,
        'message': 'Exception uploading file: $e',
      };
    }
  }
}
