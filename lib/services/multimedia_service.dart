import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:audioplayers/audioplayers.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:nsl/services/text_to_speech_service.dart';
import 'package:nsl/services/file_processing_service.dart';
import 'package:nsl/services/translation_service.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_audio_widget.dart';

/// A service class that handles all multimedia functionality:
/// - Speech-to-text
/// - Text-to-speech
/// - OCR
/// - Translation
class MultimediaService {
  // Singleton instance
  static final MultimediaService _instance = MultimediaService._internal();

  // Factory constructor
  factory MultimediaService() => _instance;

  // Internal constructor
  MultimediaService._internal();

  // Audio player
  final AudioPlayer audioPlayer = AudioPlayer();

  // Audio playback state
  String? currentPlayingMessageId;
  bool isPlaying = false;
  bool isPaused = false;
  Duration currentPosition = Duration.zero;
  Duration totalDuration = Duration.zero;
  String? currentAudioFilePath;

  // Text to speech service
  final TextToSpeechService _textToSpeechService = TextToSpeechService();

  // Speech to text
  final stt.SpeechToText _speechToText = stt.SpeechToText();
  bool _speechEnabled = false;
  bool isRecording = false;
  String _recognizedText = "";

  // Web audio recorder state
  bool _useWebAudioRecorder = false;

  // Translation service
  final TranslationService _translationService = TranslationService();

  /// Translate text to the target language
  Future<Map<String, dynamic>> translateText(String text, String targetLanguage,
      {String sourceLanguage = 'en'}) async {
    return await _translationService.translateText(
        text, sourceLanguage, targetLanguage);
  }

  // File processing service
  final FileProcessingService _fileProcessingService = FileProcessingService();

  // Callbacks
  VoidCallback? onStateChanged;
  Function(String)? onTextRecognized;
  Function(String, String, FileUploadOcrResponse)? onFileProcessed;

  /// Initialize the service
  Future<void> initialize() async {
    // Initialize speech to text
    await _initSpeech();

    // Set up audio player listeners
    _setupAudioPlayerListeners();

    // Check if we should use web audio recorder
    _useWebAudioRecorder = kIsWeb;
  }

  /// Determines if we should use the web audio recorder
  bool shouldUseWebAudioRecorder() {
    return _useWebAudioRecorder && kIsWeb;
  }

  /// Dispose resources
  void dispose() {
    audioPlayer.dispose();
  }

  /// Set up audio player listeners
  void _setupAudioPlayerListeners() {
    audioPlayer.onPositionChanged.listen((position) {
      currentPosition = position;
      _notifyStateChanged();
    });

    audioPlayer.onDurationChanged.listen((duration) {
      totalDuration = duration;
      _notifyStateChanged();
    });

    audioPlayer.onPlayerComplete.listen((_) {
      isPlaying = false;
      isPaused = false;
      currentPosition = Duration.zero;
      currentPlayingMessageId = null;
      _notifyStateChanged();
    });

    audioPlayer.onPlayerStateChanged.listen((state) {
      Logger.info('Audio player state changed: $state');

      // Update state based on player state
      isPlaying = state == PlayerState.playing;
      isPaused = state == PlayerState.paused;

      if (state == PlayerState.completed) {
        // Reset state when playback completes
        currentPosition = Duration.zero;
        currentPlayingMessageId = null;
        currentAudioFilePath = null;
      }

      _notifyStateChanged();
    });
  }

  /// Notify state changed
  void _notifyStateChanged() {
    if (onStateChanged != null) {
      onStateChanged!();
    }
  }

  /// Initialize speech recognition
  Future<void> _initSpeech() async {
    try {
      Logger.info('Initializing speech recognition...');
      _speechEnabled = await _speechToText.initialize(
        onStatus: (status) {
          Logger.info('Speech recognition status: $status');
        },
        onError: (errorNotification) {
          Logger.error(
              'Speech recognition error: ${errorNotification.errorMsg}');
          isRecording = false;
          _notifyStateChanged();
        },
      );

      if (_speechEnabled) {
        Logger.info('Speech recognition initialized successfully');
      } else {
        Logger.error('Speech recognition not available on this device');
      }
    } catch (e) {
      Logger.error('Error initializing speech recognition: $e');
      _speechEnabled = false;
    }
  }

  /// Get current language code
  String getCurrentLanguageCode(BuildContext context) {
    if (!context.mounted) return 'en';
    try {
      final locale = Localizations.localeOf(context);
      return locale.languageCode;
    } catch (e) {
      Logger.error('Error getting locale: $e, defaulting to English');
      return 'en';
    }
  }

  /// Check if translation is needed
  bool needsTranslation(BuildContext context) {
    // if (!context.mounted) return false;
    return getCurrentLanguageCode(context) != 'en';
  }

  /// Convert text to speech and play audio
  Future<void> convertTextToSpeech(
    String text, {
    String? messageId,
    required BuildContext context,
  }) async {
    if (text.isEmpty) return;

    // Generate a unique message ID if not provided
    final msgId = messageId ?? DateTime.now().millisecondsSinceEpoch.toString();

    // If we're already playing this message, toggle play/pause
    if (currentPlayingMessageId == msgId) {
      if (isPlaying) {
        Logger.info('Pausing current audio');
        await audioPlayer.pause();
        isPaused = true;
        isPlaying = false;
        _notifyStateChanged();
      } else if (isPaused) {
        Logger.info('Resuming current audio');
        await audioPlayer.resume();
        isPaused = false;
        isPlaying = true;
        _notifyStateChanged();
      }
      return;
    }

    // If we're playing a different message, stop it first
    if ((isPlaying || isPaused) &&
        currentPlayingMessageId != null &&
        currentPlayingMessageId != msgId) {
      await stopAudio();
    }

    // Store the current locale before any async operations
    String targetLanguage = 'en';
    if (context.mounted) {
      targetLanguage = getCurrentLanguageCode(context);
    }

    try {
      // Generate a cache key for this request
      final cacheKey = "$text|$targetLanguage|S1";

      // Get audio bytes from the text-to-speech service (uses cache if available)
      final audioBytes = await _textToSpeechService.getAudioBytes(
        text: text,
        targetLanguage: targetLanguage,
        voice: 'S1',
      );

      if (audioBytes != null) {
        // Play the audio bytes
        await playAudio(audioBytes, msgId);
      } else {
        Logger.error('Failed to get audio bytes from text-to-speech service');
        _resetAudioState();
      }
    } catch (e) {
      Logger.error('Error in text-to-speech conversion: $e');
      _resetAudioState();
    }
  }

  /// Play audio file
  Future<void> playAudio(Uint8List bytes, String messageId) async {
    try {
      // Check if we're already playing this file
      if (currentPlayingMessageId == messageId) {
        // If we were paused, resume playback
        if (isPaused) {
          Logger.info('Resuming audio playback: ');
          await audioPlayer.resume();

          isPlaying = true;
          isPaused = false;
          _notifyStateChanged();

          return;
        }
      }

      Logger.info('Setting audio source: ');

      await audioPlayer.setSourceBytes(bytes);
      // Set the audio source
      // await audioPlayer.setSourceUrl(filePath);

      // Play the audio
      await audioPlayer.resume();

      // Update state
      isPlaying = true;
      isPaused = false;
      currentPlayingMessageId = messageId;
      // currentAudioFilePath = filePath;
      currentPosition = Duration.zero;
      _notifyStateChanged();

      Logger.info('Playing audio file:  for message: $messageId');
    } catch (e) {
      Logger.error('Error playing audio: $e');
      _resetAudioState();
    }
  }

  /// Stop audio playback
  Future<void> stopAudio() async {
    try {
      Logger.info('Stopping audio playback');
      await audioPlayer.stop();

      _resetAudioState();
      Logger.info('Audio playback stopped successfully');
    } catch (e) {
      Logger.error('Error stopping audio playback: $e');
      _resetAudioState();
    }
  }

  /// Reset audio state
  void _resetAudioState() {
    isPlaying = false;
    isPaused = false;
    currentPlayingMessageId = null;
    currentAudioFilePath = null;
    currentPosition = Duration.zero;
    _notifyStateChanged();
  }

  /// Toggle recording (start/stop)
  Future<void> toggleRecording(BuildContext context) async {
    if (isRecording) {
      await stopSpeechRecognition();
    } else {
      await startSpeechRecognition(context);
    }
  }

  /// Start speech recognition
  Future<void> startSpeechRecognition(BuildContext context) async {
    // Check if we should use web audio recorder
    if (shouldUseWebAudioRecorder()) {
      _startWebAudioRecorder(context);
      return;
    }

    // Fallback to regular speech recognition
    if (!_speechEnabled) {
      // Try to initialize again if not enabled
      await _initSpeech();
      if (!_speechEnabled) {
        Logger.error('Speech recognition is not available');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('Speech recognition is not available on this device'),
              duration: Duration(seconds: 2),
            ),
          );
        }
        return;
      }
    }

    try {
      // Reset recognized text
      _recognizedText = "";

      isRecording = true;
      _notifyStateChanged();

      Logger.info('Starting speech recognition...');

      // Get language code before async operation
      String localeId = 'en';
      if (context.mounted) {
        localeId = getCurrentLanguageCode(context);
      }

      // Start listening without auto-stop on pause
      await _speechToText.listen(
        onResult: (result) {
          // Store the recognized text but don't update the chat field yet
          _recognizedText = result.recognizedWords;

          if (onTextRecognized != null) {
            onTextRecognized!(_recognizedText);
          }

          _notifyStateChanged();
          Logger.info('Speech recognition result: ${result.recognizedWords}');
        },
        listenFor: Duration(seconds: 300), // Listen for up to 5 minutes
        pauseFor: Duration(seconds: 60), // Set a very long pause threshold
        localeId: localeId, // Use the current language
      );

      Logger.info('Speech recognition started');
    } catch (e) {
      Logger.error('Error starting speech recognition: $e');
      isRecording = false;
      _notifyStateChanged();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting speech recognition: $e'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Start web audio recorder
  void _startWebAudioRecorder(BuildContext context) {
    try {
      // Reset recognized text
      _recognizedText = "";

      // Set recording state
      isRecording = true;
      _notifyStateChanged();

      Logger.info('Starting web audio recorder...');

      // We don't need to show the overlay anymore as the UI is handled in web_home_screen_new.dart
      // The WebAudioRecorderWidget will be displayed in the chat field
    } catch (e) {
      Logger.error('Error starting web audio recorder: $e');
      isRecording = false;
      _notifyStateChanged();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting web audio recorder: $e'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Create a WebAudioRecorderWidget
  Widget createWebAudioRecorderWidget({
    TextEditingController? chatController,
    VoidCallback? onCancel,
    Function(bool)? onLoadingChanged,
  }) {
    return WebAudioRecorderWidget(
      chatController: chatController,
      onCancel: onCancel,
      onLoadingChanged: onLoadingChanged,
      onRecordingComplete: (audioData, fileName) async {},
    );
  }

  /// Stop speech recognition
  Future<void> stopSpeechRecognition() async {
    // Check if we're using web audio recorder
    if (shouldUseWebAudioRecorder()) {
      // The WebAudioRecorderWidget now handles stopping and processing directly
      // We just need to update our state
      isRecording = false;
      _notifyStateChanged();

      Logger.info('Web audio recorder stopped');

      // If we have recognized text, notify listeners
      if (_recognizedText.isNotEmpty && onTextRecognized != null) {
        onTextRecognized!(_recognizedText);
      }

      return;
    }

    // Otherwise, use regular speech recognition
    try {
      await _speechToText.stop();
      Logger.info('Speech recognition stopped');

      isRecording = false;
      _notifyStateChanged();

      // If we have recognized text, notify listeners
      if (_recognizedText.isNotEmpty && onTextRecognized != null) {
        onTextRecognized!(_recognizedText);
      }
    } catch (e) {
      Logger.error('Error stopping speech recognition: $e');
      isRecording = false;
      _notifyStateChanged();
    }
  }

  /// Get recognized text
  String getRecognizedText() {
    return _recognizedText;
  }

  /// Process file for OCR
  Future<void> processFile(BuildContext context) async {
    final result = await _fileProcessingService.processFile();

    if (result['success']) {
      // Show success message if context is still valid
      if (context.mounted) {
        _fileProcessingService.showSuccessOverlay(
          context,
          'File uploaded: ${result['fileName']}',
        );
      }

      // Call the callback with the extracted text
      if (onFileProcessed != null) {
        onFileProcessed!(result['fileName'], result['extractedText'],
            result['ocrResponseModel']);
      }
    } else {
      // Check if it's a cancellation or an actual error
      if (result['isCanceled'] != true) {
        // Show error overlay if context is still valid
        if (context.mounted) {
          _showErrorOverlay(
            context,
            result['errorMessage'] ?? 'File upload failed',
          );
        }

        // Log the error
        Logger.error('File processing error: ${result['errorMessage']}');
      }
    }
  }

  /// Show error overlay
  void _showErrorOverlay(BuildContext context, String message) {
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 50,
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.red.shade700,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                message,
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 2 seconds
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}
