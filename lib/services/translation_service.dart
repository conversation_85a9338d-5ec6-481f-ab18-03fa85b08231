import 'package:dio/dio.dart';
import 'package:nsl/models/multimedia/translation_response_model.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/utils/logger.dart';

/// Service for handling translation API calls
class TranslationService {
  final Dio _dio = Dio();
  final AuthService _authService = AuthService();

  // Base URL for the API
  final String _baseUrl = 'http://10.26.1.52:8008/api/v1';

  /// Translate text to the target language
  ///
  /// [text] - The text to translate
  /// [targetLang] - The target language code (e.g., 'fr', 'es', 'hi-IN', 'te-IN')
  ///
  /// Returns a Map with:
  /// - success: Whether the translation was successful
  /// - translatedText: The translated text (if successful)
  /// - originalText: The original text (in case translation fails)
  /// - message: Error message (if unsuccessful)
  /// - model: The TranslationResponseModel object (if successful)
  Future<Map<String, dynamic>> translateText(
      String text, String targetLang) async {
    try {
      // Get the token
      final token = await _authService.getValidToken();

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Create request data
      final data = {
        'text': text,
        'target_lang': targetLang,
      };

      Logger.info('Translating text to $targetLang');

      // Make the API call
      final response = await _dio.post(
        '$_baseUrl/translate',
        data: data,
        options: Options(
          headers: headers,
        ),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('Translation successful');
        Logger.info(
            'Translation response structure: ${response.data.keys.join(', ')}');

        // Log the data structure if it exists
        if (response.data['data'] != null) {
          Logger.info(
              'Data structure: ${response.data['data'].keys.join(', ')}');
        }

        // Parse the response using the TranslationResponseModel
        final translationModel =
            TranslationResponseModel.fromJson(response.data);

        // Get the translated text from the model based on the new structure
        final translatedText = translationModel.data?.translatedText ?? text;

        return {
          'success': true,
          'translatedText': translatedText,
          'originalText': text,
          'model': translationModel,
        };
      } else {
        Logger.error('Translation failed: ${response.statusCode}');

        return {
          'success': false,
          'message': 'Translation failed: ${response.statusCode}',
          'originalText': text,
        };
      }
    } catch (e) {
      Logger.error('Exception during translation: $e');

      return {
        'success': false,
        'message': 'Exception during translation: $e',
        'originalText': text,
      };
    }
  }
}
